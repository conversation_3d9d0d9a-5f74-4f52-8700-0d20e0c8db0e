// eSpomienka - Custom JavaScript for elegant memorial website

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize AOS (Animate On Scroll) with error handling
    try {
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                offset: 100
            });
        } else {
            console.warn('AOS library not loaded, animations will use CSS fallbacks');
        }
    } catch (error) {
        console.warn('AOS initialization failed:', error);
    }

    // Mobile Menu Toggle
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }

    // Hero Background Slideshow with error handling
    const heroSlides = document.querySelectorAll('.hero-slide');
    let currentSlide = 0;

    function nextSlide() {
        try {
            if (heroSlides.length > 0) {
                heroSlides[currentSlide].classList.remove('active');
                currentSlide = (currentSlide + 1) % heroSlides.length;
                heroSlides[currentSlide].classList.add('active');
            }
        } catch (error) {
            console.warn('Slideshow error:', error);
        }
    }

    // Change slide every 5 seconds
    if (heroSlides.length > 1) {
        setInterval(nextSlide, 5000);
    }

    // Smooth Scrolling for Navigation Links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                try {
                    // Calculate header height for mobile offset
                    const header = document.querySelector('header');
                    const headerHeight = (header && window.innerWidth <= 768) ? header.offsetHeight : 0;
                    const targetPosition = targetSection.offsetTop - headerHeight;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    // Close mobile menu if open
                    if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                } catch (error) {
                    console.warn('Smooth scroll error:', error);
                    // Fallback to simple scroll
                    targetSection.scrollIntoView();
                }
            }
        });
    });

    // Header Background on Scroll
    const header = document.querySelector('header');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });

    // Contact Form Handling
    const contactForm = document.querySelector('.contact-form');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const formObject = {};

            formData.forEach((value, key) => {
                formObject[key] = value;
            });

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.innerHTML = '<span class="loading"></span> Odosiela sa...';
            submitBtn.disabled = true;

            // Simulate form submission (replace with actual form handling)
            setTimeout(() => {
                // Show success message
                showNotification('Ďakujeme! Vaša správa bola úspešne odoslaná. Odpovieme vám do 24 hodín.', 'success');

                // Reset form
                this.reset();

                // Reset button
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
    }

    // Newsletter Form Handling
    const newsletterForm = document.querySelector('.newsletter-form');

    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const emailInput = this.querySelector('input[type="email"]');
            const submitBtn = this.querySelector('button[type="submit"]');
            const email = emailInput.value.trim();

            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showNotification('Prosím zadajte platnú emailovú adresu.', 'error');
                return;
            }

            // Show loading state
            const originalText = submitBtn.textContent;
            submitBtn.innerHTML = '<span class="loading"></span> Prihlasuje...';
            submitBtn.disabled = true;

            // Simulate newsletter subscription (replace with actual handling)
            setTimeout(() => {
                // Show success message
                showNotification('Ďakujeme! Úspešne ste sa prihlásili na odber našich rád a inšpirácií.', 'success');

                // Reset form
                this.reset();

                // Reset button
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 1500);
        });
    }

    // Notification System
    function showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-md transform translate-x-full transition-transform duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' : 
            type === 'error' ? 'bg-red-500 text-white' : 
            'bg-blue-500 text-white'
        }`;
        
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <p class="flex-1 mr-4">${message}</p>
                <button class="text-white hover:text-gray-200 transition-colors" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }

    // Video Player Enhancement
    const video = document.getElementById('sample-video');
    
    if (video) {
        // Add custom play button overlay
        const videoContainer = video.parentElement;
        
        video.addEventListener('loadedmetadata', function() {
            // Video is ready
            console.log('Video loaded successfully');
        });
        
        video.addEventListener('error', function() {
            console.error('Error loading video');
            showNotification('Chyba pri načítavaní videa. Skúste to prosím neskôr.', 'error');
        });
    }

    // Intersection Observer for Animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.fade-in');
    animatedElements.forEach(element => {
        observer.observe(element);
    });

    // Phone Number Formatting
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    
    phoneInputs.forEach(input => {
        input.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            
            if (value.startsWith('421')) {
                value = '+' + value;
            } else if (value.startsWith('0')) {
                value = '+421' + value.substring(1);
            } else if (!value.startsWith('+')) {
                value = '+421' + value;
            }
            
            this.value = value;
        });
    });

    // Scroll to Top Button
    const scrollToTopBtn = document.createElement('button');
    scrollToTopBtn.innerHTML = `
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
        </svg>
    `;
    scrollToTopBtn.className = 'fixed bottom-8 right-8 bg-gold text-white p-3 rounded-full shadow-lg opacity-0 transition-all duration-300 hover:bg-gold/80 hover:transform hover:scale-110 z-40';
    scrollToTopBtn.style.pointerEvents = 'none';
    
    document.body.appendChild(scrollToTopBtn);
    
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 500) {
            scrollToTopBtn.style.opacity = '1';
            scrollToTopBtn.style.pointerEvents = 'auto';
        } else {
            scrollToTopBtn.style.opacity = '0';
            scrollToTopBtn.style.pointerEvents = 'none';
        }
    });

    // Preload Images and Video
    const imagesToPreload = [
        'assets/family-walking-field-nature-togetherness-concept.jpg',
        'assets/happy-woman-black-white-tone.jpg',
        'assets/happy-young-family-mother-father-two-children-son-nature-having-fun.jpg'
    ];

    imagesToPreload.forEach(src => {
        const img = new Image();
        img.src = src;
    });

    // Preload video metadata (reuse existing video variable)
    if (video) {
        video.preload = 'metadata';
        console.log('Video preloading: Štefánik 145 rokov od narodenia.mp4');
    }

    // Console Welcome Message
    console.log('%c🕊️ eSpomienka - Digitálne spomienky na milovaných', 'color: #DAA520; font-size: 16px; font-weight: bold;');
    console.log('%cVytvorené s láskou a úctou pre zachovanie spomienok.', 'color: #2C3E50; font-size: 12px;');

});

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Performance Optimization
window.addEventListener('load', function() {
    // Remove loading states
    document.body.classList.add('loaded');

    // Initialize any heavy operations after page load
    console.log('Page fully loaded');
});

// Package Navigation Functions
function navigateToPackage(packageType) {
    console.log('Navigating to package:', packageType);

    // Add loading state
    document.body.style.opacity = '0.9';

    // Show loading indicator
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    loadingIndicator.innerHTML = `
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-gold"></div>
            <span class="text-gray-700">Načítavam detaily balíčka...</span>
        </div>
    `;
    document.body.appendChild(loadingIndicator);

    const targetUrl = `balicky/${packageType}.html`;
    console.log('Target URL:', targetUrl);

    setTimeout(() => {
        window.location.href = targetUrl;
    }, 300);
}

// Enhanced Smooth Scroll for Package Links
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced smooth scroll for all anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');

    anchorLinks.forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerHeight = document.querySelector('header').offsetHeight;
                const targetPosition = target.offsetTop - headerHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Show/hide sticky CTA based on scroll
    const stickyCTA = document.querySelector('.sticky-cta');
    if (stickyCTA) {
        window.addEventListener('scroll', () => {
            if (window.scrollY > 500) {
                stickyCTA.style.display = 'block';
                stickyCTA.style.opacity = '1';
            } else {
                stickyCTA.style.opacity = '0';
                setTimeout(() => {
                    if (window.scrollY <= 500) {
                        stickyCTA.style.display = 'none';
                    }
                }, 300);
            }
        });

        // Initially hide sticky CTA
        stickyCTA.style.display = 'none';
        stickyCTA.style.opacity = '0';
    }

    // Package card hover effects enhancement and click handling
    const packageCards = document.querySelectorAll('.clickable-package');

    packageCards.forEach(card => {
        // Add click handler for navigation
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on a button or link
            if (e.target.tagName === 'A' || e.target.closest('a')) {
                return;
            }

            const href = this.getAttribute('data-href');
            if (href) {
                window.location.href = href;
            }
        });

        // Add cursor pointer style
        card.style.cursor = 'pointer';

        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
            this.style.boxShadow = '0 25px 50px -12px rgba(218, 165, 32, 0.25)';
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('featured')) {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
            } else {
                this.style.transform = 'scale(1.05) translateY(0)';
            }
        });
    });

    // FAQ accordion functionality (if needed for detail pages)
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        item.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
});

// Utility function for package page animations
function initPackagePageAnimations() {
    // Animate process steps
    const processSteps = document.querySelectorAll('.process-step');

    processSteps.forEach((step, index) => {
        step.style.opacity = '0';
        step.style.transform = 'translateX(-20px)';

        setTimeout(() => {
            step.style.transition = 'all 0.6s ease';
            step.style.opacity = '1';
            step.style.transform = 'translateX(0)';
        }, index * 200);
    });

    // Animate feature list
    const features = document.querySelectorAll('.feature-item');

    features.forEach((feature, index) => {
        feature.style.opacity = '0';
        feature.style.transform = 'translateY(20px)';

        setTimeout(() => {
            feature.style.transition = 'all 0.5s ease';
            feature.style.opacity = '1';
            feature.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Call package animations if on package page
if (window.location.pathname.includes('/balicky/')) {
    document.addEventListener('DOMContentLoaded', initPackagePageAnimations);
}
